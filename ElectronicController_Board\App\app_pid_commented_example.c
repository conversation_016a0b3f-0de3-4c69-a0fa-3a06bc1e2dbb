/**
 * @file      app_pid.c
 * @brief     激光跟踪系统PID控制模块（注释示例）
 * @details   实现双轴PID控制算法，用于激光点的精确跟踪控制
 *            支持X轴和Y轴独立的PID参数配置和控制
 */

// 主要函数的详细注释示例：

/**
 * @brief 激光坐标回调函数
 * @details 当摄像头检测到激光坐标时调用此函数
 *          根据激光颜色执行不同的操作
 * @param coord 激光坐标数据结构
 * @note 红色激光：更新当前位置（反馈信号）
 *       绿色激光：设置目标位置（期望信号）
 */
void pid_laser_coord_callback(LaserCoord_t coord)
{
    // 红色激光处理：作为当前位置反馈
    if (coord.type == RED_LASER_ID)
    {
        // 更新当前位置，用于PID控制的反馈
        app_pid_update_position(coord.x, coord.y);
    }
    // 绿色激光处理：作为目标位置设定
    else if(coord.type == GREEN_LASER_ID)
    {
        // 设置新的目标位置，PID将跟踪到此位置
        app_pid_set_target(coord.x, coord.y);
    }
}

/**
 * @brief PID控制任务函数
 * @details 由定时器回调定期执行，实现周期性PID控制
 *          只有在PID运行状态下才执行计算
 * @param timer 定时器对象指针
 * @param userData 用户数据指针（未使用）
 * @note 执行周期：10ms，与sample_time一致
 */
void app_pid_task(MultiTimer *timer, void *userData)
{
    // 检查PID是否处于运行状态
    if (pid_running)
    {
        // 执行PID控制计算
        app_pid_calc();

        // 重新启动定时器，实现周期性调用
        // 周期时间 = sample_time * 1000 (转换为毫秒)
        multiTimerStart(&mt_pid, (uint32_t)(pid_params_x.sample_time * 1000), app_pid_task, NULL);
    }
}

/**
 * @brief 启动PID控制
 * @details 初始化并启动PID控制系统，开始激光跟踪
 *          设置激光坐标回调，重置PID状态，启动控制任务
 * @note 如果PID已经在运行，函数会直接返回
 */
void app_pid_start(void)
{
    // 如果PID控制已经在运行，直接返回
    if (pid_running)
    {
        return;
    }

#if DEBUG_PID
    my_printf(&PID_REPORT_UART, "启动PID控制\n");
#endif

    // 设置激光坐标回调函数，接收摄像头数据
    maixcam_set_callback(pid_laser_coord_callback);

    // 重置PID控制器状态，清除历史数据
    pid_reset(&pid_x);
    pid_reset(&pid_y);

    // 设置PID运行标志
    pid_running = true;

    // 启动PID控制定时器，开始周期性控制
    multiTimerStart(&mt_pid, (uint32_t)(pid_params_x.sample_time * 1000), app_pid_task, NULL);

    // 如果启用PID信息上报，启动上报定时器
#if PID_REPORT_ENABLE
    multiTimerStart(&mt_pid_report, PID_REPORT_PERIOD, app_pid_report_task, NULL);
#endif
}

/**
 * @brief 停止PID控制
 * @details 停止PID控制系统，停止激光跟踪
 *          停止电机，停止定时器，恢复默认回调
 * @note 如果PID已经停止，函数会直接返回
 */
void app_pid_stop(void)
{
    // 如果PID控制已经停止，直接返回
    if (!pid_running)
    {
        return;
    }

#if DEBUG_PID
    my_printf(&PID_REPORT_UART, "停止PID控制\n");
#endif

    // 立即停止电机运动
    Motor_Stop();

    // 停止PID控制定时器
    multiTimerStop(&mt_pid);

    // 如果启用PID信息上报，停止上报定时器
#if PID_REPORT_ENABLE
    multiTimerStop(&mt_pid_report);
#endif

    // 清除PID运行标志
    pid_running = false;

    // 恢复默认回调函数
    maixcam_set_callback(NULL);
}

/**
 * @brief 设置X轴PID参数
 * @details 动态更新X轴PID控制器的参数
 *          可在运行时调整控制性能
 * @param params 新的PID参数结构体
 * @note 参数立即生效，无需重启PID控制
 */
void app_pid_set_x_params(PidParams_t params)
{
    // 更新参数结构体
    pid_params_x = params;

    // 更新PID控制器内部参数
    pid_set_params(&pid_x, params.kp, params.ki, params.kd);
    pid_set_limit(&pid_x, params.out_max);
}

/**
 * @brief 设置Y轴PID参数
 * @details 动态更新Y轴PID控制器的参数
 * @param params 新的PID参数结构体
 */
void app_pid_set_y_params(PidParams_t params)
{
    // 更新参数结构体
    pid_params_y = params;

    // 更新PID控制器内部参数
    pid_set_params(&pid_y, params.kp, params.ki, params.kd);
    pid_set_limit(&pid_y, params.out_max);
}

/**
 * @brief 解析上位机目标值
 * @details 将上位机发送的缩放值转换为实际像素值
 * @param scale_value 缩放后的值
 * @return 实际像素值
 * @note 缩放比例由PID_REPORT_SCALE定义
 */
int app_pid_parse_target(int scale_value)
{
    // 将上位机数据格式转换为实际坐标值
    return scale_value / PID_REPORT_SCALE;
}

/**
 * @brief PID信息上报函数
 * @details 按照指定格式向上位机发送PID运行状态
 *          包括目标值、实际值、控制输出等信息
 * @note 数据格式：{标识}目标值,实际值\n
 */
void app_pid_report(void)
{
    // 将PID目标值和实际值缩放转换为整数
    int scaled_target_x = (int)(target_x * PID_REPORT_SCALE);
    int scaled_current_x = (int)(current_x * PID_REPORT_SCALE);
    int scaled_target_y = (int)(target_y * PID_REPORT_SCALE);
    int scaled_current_y = (int)(current_y * PID_REPORT_SCALE);

    // 按照{name}a,b\n格式发送PID信息
    my_printf(&PID_REPORT_UART, "{X}%d,%d\n", scaled_target_x, scaled_current_x);
    my_printf(&PID_REPORT_UART, "{Y}%d,%d\n", scaled_target_y, scaled_current_y);
    my_printf(&PID_REPORT_UART, "{V}%d,%d\n", motor_x, motor_y);

    // 发送详细调试信息（可选）
#if PID_DEBUG_ENABLE
    my_printf(&PID_REPORT_UART, "{PX}%.2f,%.2f,%.2f\n", pid_x.p_out, pid_x.i_out, pid_x.d_out);
    my_printf(&PID_REPORT_UART, "{PY}%.2f,%.2f,%.2f\n", pid_y.p_out, pid_y.i_out, pid_y.d_out);
#endif
}

/**
 * @brief 解析上位机指令
 * @details 解析并执行来自上位机的各种控制命令
 *          支持目标设置、参数调节、启动停止等操作
 * @param cmd 上位机指令字符串
 * @note 支持的命令格式：
 *       T:x,y - 设置目标位置
 *       PX:kp,ki,kd - 设置X轴PID参数
 *       PY:kp,ki,kd - 设置Y轴PID参数
 *       START - 启动控制
 *       STOP - 停止控制
 */
void app_pid_parse_cmd(char *cmd)
{
    int val1, val2, val3;

    // 解析上位机设置的目标位置 - T:x,y
    if (sscanf(cmd, "T:%d,%d", &val1, &val2) == 2)
    {
        target_x = app_pid_parse_target(val1);
        target_y = app_pid_parse_target(val2);

        // 设置PID目标值
        pid_set_target(&pid_x, (float)target_x);
        pid_set_target(&pid_y, (float)target_y);
    }
    // 设置X轴PID控制参数 - PX:kp,ki,kd
    else if (sscanf(cmd, "PX:%d,%d,%d", &val1, &val2, &val3) == 3)
    {
        PidParams_t new_params_x = pid_params_x;
        new_params_x.kp = val1 / 100.0f;  // 参数除以100转换为浮点数
        new_params_x.ki = val2 / 100.0f;
        new_params_x.kd = val3 / 100.0f;
        app_pid_set_x_params(new_params_x);
    }
    // 设置Y轴PID控制参数 - PY:kp,ki,kd
    else if (sscanf(cmd, "PY:%d,%d,%d", &val1, &val2, &val3) == 3)
    {
        PidParams_t new_params_y = pid_params_y;
        new_params_y.kp = val1 / 100.0f;
        new_params_y.ki = val2 / 100.0f;
        new_params_y.kd = val3 / 100.0f;
        app_pid_set_y_params(new_params_y);
    }
    // 处理启动/停止命令
    else if (strncmp(cmd, "START", 5) == 0)
    {
        app_pid_start();  // 启动PID控制
    }
    else if (strncmp(cmd, "STOP", 4) == 0)
    {
        app_pid_stop();   // 停止PID控制
    }
    // 直接设置位置命令 - R: X=110, Y=102
    else if (sscanf(cmd, "R: X=%d, Y=%d", &val1, &val2) == 2)
    {
        target_x = val1;
        target_y = val2;

        // 设置PID目标值
        pid_set_target(&pid_x, (float)target_x);
        pid_set_target(&pid_y, (float)target_y);

        // 如果PID未运行，自动启动
        if (!pid_running)
        {
            app_pid_start();
        }
    }
}

/*
 * ============================================================================
 * PID控制系统设计说明
 * ============================================================================
 * 
 * 1. 控制架构：
 *    - 双轴独立PID控制（X轴水平，Y轴垂直）
 *    - 位置式PID算法
 *    - 10ms控制周期，保证实时性
 * 
 * 2. 信号流向：
 *    绿色激光 → 目标位置 → PID设定值
 *    红色激光 → 当前位置 → PID反馈值
 *    PID输出 → 电机控制 → 机械运动
 * 
 * 3. 保护机制：
 *    - 死区控制：±1像素内不动作
 *    - 积分限幅：防止积分饱和
 *    - 输出限幅：±99%电机速度
 *    - 坐标检查：防止异常数据
 * 
 * 4. 参数调节：
 *    - 支持在线参数调整
 *    - 独立的X/Y轴参数
 *    - 上位机实时监控
 * 
 * ============================================================================
 */
