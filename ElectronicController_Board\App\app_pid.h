// Copyright (c) 2024 米醋电子工作室

#ifndef __APP_PID_H
#define __APP_PID_H

#include "main.h"
#include "pid.h"
#include "mydefine.h"

// 上位机接口相关定义
#define PID_REPORT_ENABLE 1    // 开启PID信息上报
#define PID_DEBUG_ENABLE 0     // 开启详细调试信息
#define PID_REPORT_PERIOD 20   // 上报周期 (ms)
#define PID_REPORT_SCALE 100   // 上报数据放大系数
#define PID_REPORT_UART huart1 // 上报使用的串口



// PID参数结构体
typedef struct
{
    float kp;          // 比例系数
    float ki;          // 积分系数
    float kd;          // 微分系数
    float sample_time; // 采样时间
    float out_min;     // 输出最小值
    float out_max;     // 输出最大值
    float i_min;       // 积分项最小值
    float i_max;       // 积分项最大值
    float deadzone;    // 死区大小
} PidParams_t;

// 函数声明
void app_pid_init(void);
void app_pid_set_target(int x, int y);
void app_pid_update_position(int x, int y);
void app_pid_start(void);
void app_pid_stop(void);
void app_pid_calc(void);
void app_pid_report(void);
void app_pid_parse_cmd(char *cmd);
void app_pid_set_x_params(PidParams_t params);
void app_pid_set_y_params(PidParams_t params);
void app_pid_report_task(MultiTimer *timer, void *userData);
void app_pid_task(MultiTimer *timer, void *userData);


#endif /* __APP_PID_H */
