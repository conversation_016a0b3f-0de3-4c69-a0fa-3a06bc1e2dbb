/**
 * @file      app_maixcam.c
 * @brief     MaixCam摄像头数据处理模块
 * @details   负责处理MaixCam摄像头发送的激光坐标数据，包括数据接收、解析、
 *            校验和回调处理。支持红色和绿色激光点的坐标识别和跟踪。
 * <AUTHOR>
 * @date      2024
 * @version   1.0
 * @copyright Copyright (c) 2024 米醋电子工作室
 * 
 * @note      数据格式：$激光类型,X坐标,Y坐标,校验和\n
 *            示例：$R,320,240,A5\n (红色激光在坐标320,240)
 *                 $G,160,120,B2\n (绿色激光在坐标160,120)
 */

#include "app_maixcam.h"

/**
 * @brief 默认激光坐标处理回调函数
 * @details 当没有设置自定义回调函数时，使用此默认处理函数
 *          红色激光：初始化PID并开始跟踪
 *          绿色激光：仅打印坐标信息
 * @param coord 激光坐标数据结构
 * @note 红色激光通常用作当前位置反馈，绿色激光用作目标位置设定
 */
static void default_laser_callback(LaserCoord_t coord)
{
    // 根据激光类型进行不同的处理
    if (coord.type == RED_LASER_ID)
    {
        // 红色激光处理流程：
        // 1. 初始化PID控制器（第一次读取激光数据时）
        app_pid_init();
        
        // 2. 设置默认目标位置（屏幕中心附近）
        app_pid_set_target(100, 100);
        
        // 3. 打印红色激光坐标到串口（用于调试）
        my_printf(&huart1, "R: X=%d, Y=%d\r\n", coord.x, coord.y);
        
        // 4. 启动PID控制开始跟踪
        app_pid_start();
    }
    else if (coord.type == GREEN_LASER_ID)
    {
        // 绿色激光处理：仅打印坐标信息
        // 通常绿色激光用于设置目标位置或标记功能
        my_printf(&huart1, "G: X=%d, Y=%d\r\n", coord.x, coord.y);
    }
}

/**
 * @brief 激光坐标回调函数指针
 * @details 存储当前使用的激光坐标处理回调函数地址
 *          默认指向default_laser_callback函数
 *          可通过maixcam_set_callback()函数进行动态切换
 */
static LaserCoordCallback_t laser_coord_callback = default_laser_callback;

/**
 * @brief 设置激光坐标数据处理回调函数
 * @details 允许用户自定义激光坐标的处理逻辑，实现不同的跟踪策略
 *          如果传入NULL，则恢复为默认回调函数
 * @param callback 新的回调函数指针
 * @note 回调函数应该具有签名：void callback(LaserCoord_t coord)
 * @example 
 *          // 设置自定义回调
 *          maixcam_set_callback(my_custom_callback);
 *          // 恢复默认回调
 *          maixcam_set_callback(NULL);
 */
void maixcam_set_callback(LaserCoordCallback_t callback)
{
    if (callback != NULL)
        laser_coord_callback = callback;  // 设置新的回调函数
    else
        laser_coord_callback = default_laser_callback;  // 恢复默认回调
}

/**
 * @brief MaixCam数据解析函数
 * @details 解析MaixCam发送的激光坐标数据，包含完整的数据校验流程
 *          支持的数据格式：$激光类型,X坐标,Y坐标,校验和\n
 * @param buffer 接收到的数据缓冲区（C风格字符串）
 * @return int 解析结果
 *         @retval  0  解析成功
 *         @retval -1  帧头错误（不是'$'开头）
 *         @retval -2  数据格式错误（找不到校验和分隔符）
 *         @retval -3  校验和格式错误（无法解析十六进制）
 *         @retval -4  校验和验证失败（数据可能损坏）
 *         @retval -5  坐标解析失败（数据格式不正确）
 *         @retval -6  未知的激光类型（不是R或G）
 * @note 数据示例：$R,320,240,A5\n
 *       其中：R=红色激光，320=X坐标，240=Y坐标，A5=校验和
 */
int maixcam_parse_data(char *buffer)
{
    // 第1步：基本参数检查
    if (!buffer || buffer[0] != FRAME_HEADER)
        return -1; // 帧头错误：数据必须以'$'开头

    // 第2步：查找最后一个逗号的位置（校验和分隔符）
    // 数据格式：$R,320,240,A5 -> 最后一个逗号在240后面
    char *last_comma = strrchr(buffer, ',');
    if (!last_comma)
        return -2; // 数据格式错误：找不到校验和分隔符

    // 第3步：计算数据校验和（从帧头到最后一个逗号之前）
    int calc_checksum = 0;
    char *p = buffer;
    while (p < last_comma)
    {
        calc_checksum += (unsigned char)*p;  // 累加每个字符的ASCII值
        p++;
    }
    calc_checksum &= 0xFF; // 取低8位作为校验和

    // 第4步：解析接收到的校验和（十六进制格式）
    int actual_checksum = 0;
    if (sscanf(last_comma + 1, "%X", &actual_checksum) != 1)
        return -3; // 校验和格式错误：无法解析为十六进制数

    // 第5步：校验和验证
    if (calc_checksum != actual_checksum)
        return -4; // 校验和验证失败：数据传输可能有误

    // 第6步：解析激光坐标数据
    LaserCoord_t coord;
    int parsed = sscanf(buffer, "$%c,%d,%d", &coord.type, &coord.x, &coord.y);

    if (parsed != 3)
        return -5; // 坐标解析失败：数据格式不符合预期

    // 第7步：验证激光类型的有效性
    if (coord.type != RED_LASER_ID && coord.type != GREEN_LASER_ID)
        return -6; // 未知的激光类型：只支持'R'(红色)和'G'(绿色)

    // 第8步：通过回调函数处理解析后的激光坐标
    if (laser_coord_callback)
        laser_coord_callback(coord);

    return 0; // 解析成功
}

/**
 * @brief MaixCam摄像头数据处理任务
 * @details 这是激光跟踪系统的核心数据处理函数，负责从环形缓冲区读取
 *          MaixCam发送的激光坐标数据，并进行解析处理
 * @param timer 多任务定时器指针（用于任务调度）
 * @param userData 用户数据指针（本函数中未使用）
 * @note 该函数通过串口中断触发，处理格式如：$R,320,240,A5\n
 */
void maixcam_task(MultiTimer *timer, void *userData)
{
    // 第1步：检查环形缓冲区中是否有待处理的数据
    // rt_ringbuffer_data_len() 返回缓冲区中可读数据的字节数
    int length_cam = rt_ringbuffer_data_len(&ringbuffer_cam);
    
    // 第2步：只有当缓冲区中有数据时才进行处理
    if (length_cam > 0)
    {
        // 第3步：从环形缓冲区读取数据到输出缓冲区
        // ringbuffer_cam: 存储MaixCam数据的环形缓冲区
        // output_buffer_cam: 64字节的输出缓冲区，用于临时存储读取的数据
        // length_cam: 要读取的数据长度
        rt_ringbuffer_get(&ringbuffer_cam, output_buffer_cam, length_cam);
        
        // 第4步：在数据末尾添加字符串结束符
        // 将字节数组转换为C风格字符串，便于后续字符串处理函数使用
        output_buffer_cam[length_cam] = '\0';
        
        // 第5步：调用数据解析函数处理激光坐标
        // 解析MaixCam发送的激光坐标数据（如：$R,320,240,A5）
        // result保存解析结果：0表示成功，负数表示各种错误码
        int result = maixcam_parse_data((char *)output_buffer_cam);
        
        // 第6步：清空输出缓冲区，防止旧数据干扰下次处理
        // 将已处理的数据区域清零，确保缓冲区干净状态
        memset(output_buffer_cam, 0, length_cam);
    }
    
    // 注意：该函数执行完毕后，多任务调度器会根据需要重新调度其他任务
    // 新的MaixCam数据到达时，串口中断会再次触发此任务
}

/* 
 * 注释掉的代码：
 * multiTimerStart(&mt_pid, 10, app_pid_task, NULL);
 * 
 * 说明：这行代码原本可能用于在检测到激光后立即启动PID任务
 *       现在PID任务通过其他机制启动，所以被注释掉了
 */
