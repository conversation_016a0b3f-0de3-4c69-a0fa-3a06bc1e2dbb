/**
 * @file      MultiTimer.c
 * @brief     多任务定时器模块实现
 * @details   实现了一个轻量级的协作式多任务调度器，基于时间触发机制
 *            支持多个定时器任务的并发执行，适用于嵌入式系统的任务管理
 *
 *            主要特性：
 *            - 基于链表的定时器管理
 *            - 按到期时间自动排序
 *            - 支持动态添加/删除定时器
 *            - 非抢占式任务调度
 *            - 低内存占用，高效执行
 *
 * <AUTHOR>
 * @version   1.0
 * @date      2024
 *
 * @note      在激光跟踪系统中用于管理：
 *            - OLED显示任务 (500ms)
 *            - PID控制任务 (10ms)
 *            - 摄像头数据处理任务 (50ms)
 *            - 电机状态监控任务 (20ms)
 *            - 用户命令处理任务 (100ms)
 */

#include "MultiTimer.h"
#include <stdio.h>

/**
 * @brief 定时器链表头指针
 * @details 指向按到期时间排序的定时器链表，最早到期的定时器在链表头部
 *          使用静态变量确保全局唯一性和数据持久性
 */
static MultiTimer* timerList = NULL;

/**
 * @brief 平台时钟函数指针
 * @details 存储获取系统时间戳的函数指针，由用户在初始化时提供
 *          通常指向HAL_GetTick()或其他系统时钟函数
 */
static PlatformTicksFunction_t platformTicksFunction = NULL;

/**
 * @brief 安装平台时钟函数
 * @details 设置系统时钟获取函数，这是多任务定时器系统的基础
 *          必须在使用任何定时器功能之前调用此函数
 * @param ticksFunc 平台时钟函数指针，通常是HAL_GetTick或类似函数
 * @return int 安装结果
 *         @retval  0  安装成功
 *         @retval -1  安装失败（函数指针为空）
 * @note 在激光跟踪系统中，通常传入bsp_get_systick函数
 * @example
 *          // 系统初始化时调用
 *          multiTimerInstall(bsp_get_systick);
 */
int multiTimerInstall(PlatformTicksFunction_t ticksFunc) {
    // 参数有效性检查
    if (ticksFunc == NULL) {
        return -1; // 时钟函数指针不能为空
    }

    // 保存时钟函数指针，供后续定时器使用
    platformTicksFunction = ticksFunc;
    return 0; // 安装成功
}

/**
 * @brief 从定时器链表中移除指定定时器
 * @details 遍历定时器链表，找到指定的定时器并将其从链表中移除
 *          使用二级指针技术，简化链表操作逻辑
 * @param timer 要移除的定时器指针
 * @note 这是一个内部函数，用于定时器的启动和停止操作
 *       如果定时器不在链表中，函数会安全返回而不产生错误
 */
static void removeTimer(MultiTimer* timer) {
    // 使用二级指针遍历链表，current指向当前节点的指针
    MultiTimer** current = &timerList;

    // 遍历整个定时器链表
    while (*current) {
        // 找到要移除的定时器
        if (*current == timer) {
            // 将当前节点从链表中移除（跳过当前节点）
            *current = timer->next;
            break; // 找到并移除后退出循环
        }
        // 移动到下一个节点
        current = &(*current)->next;
    }
}

/**
 * @brief 启动一个定时器任务
 * @details 将定时器添加到系统中，并按到期时间插入到有序链表中
 *          如果定时器已经在运行，会先移除再重新添加
 * @param timer 定时器对象指针
 * @param timing 定时时间（毫秒），从当前时间开始计算
 * @param callback 定时器到期时的回调函数
 * @param userData 传递给回调函数的用户数据指针
 * @return int 启动结果
 *         @retval  0  启动成功
 *         @retval -1  启动失败（参数无效或系统未初始化）
 * @note 在激光跟踪系统中的使用示例：
 *       multiTimerStart(&mt_pid, 10, pid_task, NULL);     // 10ms PID任务
 *       multiTimerStart(&mt_oled, 500, oled_task, NULL);  // 500ms显示任务
 */
int multiTimerStart(MultiTimer* timer, uint64_t timing, MultiTimerCallback_t callback, void* userData) {
    // 参数有效性检查
    if (!timer || !callback || platformTicksFunction == NULL) {
        return -1; // 参数无效或系统未初始化
    }

    // 如果定时器已经在链表中，先移除它（避免重复添加）
    removeTimer(timer);

    // 设置定时器参数
    timer->deadline = platformTicksFunction() + timing;  // 计算到期时间戳
    timer->callback = callback;                          // 设置回调函数
    timer->userData = userData;                          // 设置用户数据

    // 按到期时间将定时器插入到有序链表中（最早到期的在前面）
    MultiTimer** current = &timerList;

    // 找到合适的插入位置（保持链表按到期时间排序）
    while (*current && ((*current)->deadline < timer->deadline)) {
        current = &(*current)->next;
    }

    // 插入定时器到链表中
    timer->next = *current;  // 新定时器指向后续节点
    *current = timer;        // 前一个节点指向新定时器

    return 0; // 启动成功
}

/**
 * @brief 停止一个定时器任务
 * @details 将指定的定时器从系统中移除，停止其执行
 *          如果定时器不在运行中，函数会安全返回
 * @param timer 要停止的定时器对象指针
 * @return int 停止结果
 *         @retval 0 停止成功（无论定时器是否在运行）
 * @note 停止后的定时器可以重新启动
 * @example
 *          // 停止PID控制任务
 *          multiTimerStop(&mt_pid);
 */
int multiTimerStop(MultiTimer* timer) {
    // 使用统一的移除函数从链表中移除定时器
    removeTimer(timer);
    return 0; // 总是返回成功
}

/**
 * @brief 多任务定时器调度函数 - 系统核心调度器
 * @details 这是整个多任务系统的心脏，负责检查和执行到期的定时器任务
 *          在主循环中被持续调用，实现非阻塞的任务调度
 * @return int 调度结果
 *         @retval -1  平台时钟函数未初始化（错误状态）
 *         @retval  0  当前没有待执行的定时器任务
 *         @retval >0  距离下一个定时器到期的时间（毫秒）
 * @note 这个函数必须在主循环中频繁调用以保证任务的实时性
 * @example
 *          // 在main函数的主循环中
 *          while(1) {
 *              multiTimerYield();  // 执行到期的任务
 *          }
 */
int multiTimerYield(void) {
    // 检查平台时钟函数是否已初始化
    if (platformTicksFunction == NULL) {
        return -1; // 返回错误码，表示时钟函数未设置
    }

    // 获取当前系统时钟节拍数（通常是毫秒级时间戳）
    uint64_t currentTicks = platformTicksFunction();

    // 遍历定时器链表，执行所有已到期的定时器
    // 链表按到期时间排序，最早到期的在链表头部
    while (timerList && (currentTicks >= timerList->deadline)) {
        // 获取当前到期的定时器
        MultiTimer* timer = timerList;

        // 从链表中移除已到期的定时器（更新链表头指针）
        timerList = timer->next;

        // 检查定时器是否有有效的回调函数
        if (timer->callback) {
            // 执行定时器的回调函数
            // 传入定时器对象本身和用户数据指针
            timer->callback(timer, timer->userData);
        }
    }

    // 返回距离下一个定时器到期的时间
    // 如果还有定时器：返回剩余时间（毫秒）
    // 如果没有定时器：返回0
    return timerList ? (int)(timerList->deadline - currentTicks) : 0;
}

/*
 * ============================================================================
 * 多任务定时器系统设计说明
 * ============================================================================
 *
 * 1. 系统架构：
 *    - 基于单链表的定时器管理
 *    - 按到期时间自动排序（最早到期的在链表头）
 *    - 协作式任务调度（非抢占式）
 *    - 事件驱动的执行模式
 *
 * 2. 关键特性：
 *    - 轻量级：内存占用小，适合嵌入式系统
 *    - 高效性：O(n)插入，O(1)执行最早任务
 *    - 灵活性：支持动态添加/删除任务
 *    - 安全性：参数检查和错误处理
 *
 * 3. 在激光跟踪系统中的应用：
 *    - PID控制任务：10ms周期，最高优先级（最短周期）
 *    - 电机监控任务：20ms周期，监控电机状态
 *    - 摄像头数据任务：50ms周期，处理激光坐标
 *    - 用户交互任务：100ms周期，处理用户命令
 *    - OLED显示任务：500ms周期，更新显示内容
 *
 * 4. 使用注意事项：
 *    - 必须先调用multiTimerInstall()初始化系统
 *    - 主循环中必须持续调用multiTimerYield()
 *    - 任务回调函数不应执行耗时操作
 *    - 任务可以在回调中重新启动自己实现周期执行
 *
 * 5. 性能特点：
 *    - 时间复杂度：插入O(n)，删除O(n)，执行O(1)
 *    - 空间复杂度：O(n)，n为定时器数量
 *    - 实时性：取决于任务执行时间和调用频率
 *
 * ============================================================================
 */
