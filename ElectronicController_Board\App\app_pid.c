/**
 * @file      app_pid.c
 * @brief     激光跟踪系统PID控制模块
 * @details   实现双轴PID控制算法，用于激光点的精确跟踪控制
 *            支持X轴和Y轴独立的PID参数配置和控制
 *
 *            主要功能：
 *            - 双轴PID控制器管理
 *            - 激光坐标数据处理
 *            - 电机速度控制输出
 *            - 参数动态调节
 *            - 实时状态监控和报告
 *
 * <AUTHOR>
 * @date      2024
 * @version   1.0
 * @copyright Copyright (c) 2024 米醋电子工作室
 *
 * @note      PID控制周期：10ms
 *            控制范围：X轴0-1920像素，Y轴0-1080像素
 *            输出范围：±99%电机速度
 */

#include "app_pid.h"
#include "pid.h"

/**
 * @brief PID控制器实例定义
 * @details 为X轴和Y轴分别创建独立的PID控制器
 *          支持不同的PID参数配置以适应不同轴的特性
 */
PID_T pid_x; // X轴PID控制器（水平方向）
PID_T pid_y; // Y轴PID控制器（垂直方向）

/**
 * @brief X轴PID控制参数配置
 * @details 针对水平方向运动特性优化的PID参数
 *          经过实际测试调优，适合激光跟踪的快速响应需求
 */
PidParams_t pid_params_x = {
    .kp = 2.0f,           // 比例系数Kp：控制响应速度，值越大响应越快
    .ki = 0.008f,         // 积分系数Ki：消除静态误差，值过大会引起震荡
    .kd = 0.015f,         // 微分系数Kd：提高稳定性，抑制超调
    .sample_time = 0.01f, // 采样时间：10ms，与任务调度周期一致
    .out_min = -99.0f,    // 输出下限：-99%电机速度（反向最大速度）
    .out_max = 99.0f,     // 输出上限：+99%电机速度（正向最大速度）
    .i_min = -80.0f,      // 积分下限：防止积分饱和
    .i_max = 80.0f,       // 积分上限：防止积分饱和
    .deadzone = 1         // 死区范围：±1像素内不进行控制，减少微小抖动
};

/**
 * @brief Y轴PID控制参数配置
 * @details 针对垂直方向运动特性优化的PID参数
 *          Y轴微分系数略小，适应垂直运动的特点
 */
PidParams_t pid_params_y = {
    .kp = 2.0f,           // 比例系数：与X轴相同
    .ki = 0.008f,         // 积分系数：与X轴相同
    .kd = 0.008f,         // 微分系数：比X轴略小，适应Y轴特性
    .sample_time = 0.01f, // 采样时间：10ms
    .out_min = -99.0f,    // 输出下限
    .out_max = 99.0f,     // 输出上限
    .i_min = -80.0f,      // 积分下限
    .i_max = 80.0f,       // 积分上限
    .deadzone = 1         // 死区范围：±1像素
};

// PIDĿ������
int target_x = 320; // Ĭ����Ļ����
int target_y = 240; // Ĭ����Ļ����

// ��ǰʵ������
int current_x = 320;
int current_y = 240;

// PID����״̬��־
static bool pid_running = false;

// PID��ʱ��
static MultiTimer mt_pid;

// PID��Ϣ�ϱ���ʱ��
static MultiTimer mt_pid_report;

// ������ֵ
int8_t motor_x, motor_y;

// ���Ա�־����ʾ������Ϣ
#define DEBUG_PID 0

// ���ٱ��ʹ�����
#define CONSTRAIN(x, min, max) ((x) < (min) ? (min) : ((x) > (max) ? (max) : (x)))

/**
 * @brief 积分限幅函数
 * @details 防止PID控制器的积分项过大导致系统不稳定
 *          当积分值超出设定范围时，将其限制在合理范围内
 * @param pid PID控制器指针
 * @param min 积分项最小值
 * @param max 积分项最大值
 * @note 积分限幅是PID控制中的重要技术，防止积分饱和现象
 */
static void app_pid_limit_integral(PID_T *pid, float min, float max)
{
    // 如果积分值超过上限，限制为最大值
    if (pid->integral > max)
    {
        pid->integral = max;
    }
    // 如果积分值低于下限，限制为最小值
    else if (pid->integral < min)
    {
        pid->integral = min;
    }
}

/**
 * @brief 激光跟踪PID控制器初始化
 * @details 初始化X轴和Y轴PID控制器，设置初始参数和状态
 *          在系统启动时调用，为激光跟踪做准备
 * @note 初始化后PID控制器处于停止状态，需要调用app_pid_start()启动
 */
void app_pid_init(void)
{
    // 初始化X轴PID控制器
    // 参数：控制器指针，Kp，Ki，Kd，初始输入值，输出限制
    pid_init(&pid_x,
             pid_params_x.kp, pid_params_x.ki, pid_params_x.kd,
             (float)current_x, pid_params_x.out_max);

    // 初始化Y轴PID控制器
    // 使用独立的Y轴参数，适应垂直方向的运动特性
    pid_init(&pid_y,
             pid_params_y.kp, pid_params_y.ki, pid_params_y.kd,
             (float)current_y, pid_params_y.out_max);

    // 调试模式下自动启动PID控制（仅用于开发调试）
#if DEBUG_PID
    app_pid_start();
#endif

    // 如果启用PID信息上报，启动上报定时器
    // 用于向上位机发送PID运行状态和参数信息
#if PID_REPORT_ENABLE
    multiTimerStart(&mt_pid_report, PID_REPORT_PERIOD, app_pid_report_task, NULL);
#endif
}

/**
 * @brief 设置PID控制器的目标坐标
 * @details 设置激光跟踪的目标位置，通常由绿色激光指示或用户命令设定
 *          目标坐标是PID控制的期望值
 * @param x X轴目标坐标（像素）
 * @param y Y轴目标坐标（像素）
 * @note 坐标范围：X轴0-1920，Y轴0-1080
 */
void app_pid_set_target(int x, int y)
{
#if DEBUG_PID
    my_printf(&PID_REPORT_UART, "设置目标位置: X=%d, Y=%d\n", x, y);
#endif
    // 更新全局目标坐标变量
    target_x = x;
    target_y = y;

    // 将目标值设置到对应的PID控制器中
    pid_set_target(&pid_x, (float)target_x);
    pid_set_target(&pid_y, (float)target_y);
}

/**
 * @brief 更新当前实际坐标
 * @details 更新激光跟踪的当前位置，通常由红色激光反馈提供
 *          这是PID控制的反馈信号，用于计算控制误差
 * @param x X轴当前坐标（像素）
 * @param y Y轴当前坐标（像素）
 * @note 坐标有效性检查：X轴0-1920，Y轴0-1080
 */
void app_pid_update_position(int x, int y)
{
    // 检查坐标有效性，防止异常数据影响控制
    if (x < 0 || x > 1920 || y < 0 || y > 1080)
    {
        return; // 无效坐标直接丢弃，不更新位置
    }

    // 更新全局当前坐标变量
    current_x = x;
    current_y = y;
}

/**
 * @brief 执行PID控制计算
 * @details 这是PID控制的核心函数，计算控制误差并输出电机控制量
 *          每10ms调用一次，实现闭环控制
 * @note 包含死区控制、积分限幅、输出限幅等保护机制
 */
void app_pid_calc(void)
{
    int error_x, error_y;
    float output_x, output_y;

    // ����X�����
    error_x = target_x - current_x;
    error_y = target_y - current_y;

#if DEBUG_PID
    my_printf(&PID_REPORT_UART, "���X=%d Y=%d\n", error_x, error_y);
#endif

    // ֻ��ͬʱ�������ڲ�ֹͣ���
    if (abs(error_x) <= pid_params_x.deadzone && abs(error_y) <= pid_params_y.deadzone)
    {
#if DEBUG_PID
        my_printf(&PID_REPORT_UART, "�������ڣ�ֹͣ���\n");
#endif
        Motor_Stop();
        return;
    }

    // ʹ��λ��ʽPID����X�����
    output_x = pid_calculate_positional(&pid_x, (float)current_x);

    // ִ�л����޷�
    app_pid_limit_integral(&pid_x, pid_params_x.i_min, pid_params_x.i_max);

    // ʹ��λ��ʽPID����Y�����
    output_y = pid_calculate_positional(&pid_y, (float)current_y);

    // ִ�л����޷�
    app_pid_limit_integral(&pid_y, pid_params_y.i_min, pid_params_y.i_max);

    // �޷�����
    output_x = CONSTRAIN(output_x, pid_params_x.out_min, pid_params_x.out_max);
    output_y = CONSTRAIN(output_y, pid_params_y.out_min, pid_params_y.out_max);

    // ��ȡ���յ������ֵ
    motor_x = (int8_t)output_x;
    motor_y = (int8_t)output_y;

#if DEBUG_PID
    my_printf(&PID_REPORT_UART, "PID���: X=%d, Y=%d\n", motor_x, motor_y);
#endif

    // ���Ƶ��
    Motor_Set_Speed(-motor_x, -motor_y);
}

/**
 * @brief 激光坐标回调函数
 * @details 当摄像头检测到激光坐标时调用此函数
 *          根据激光颜色执行不同的控制操作
 * @param coord 激光坐标数据结构
 * @note 红色激光：作为当前位置反馈，用于PID控制的测量值
 *       绿色激光：作为目标位置设定，用于PID控制的设定值
 */
void pid_laser_coord_callback(LaserCoord_t coord)
{
    // 红色激光处理：更新当前位置（PID反馈信号）
    if (coord.type == RED_LASER_ID)
    {
        // 将红色激光坐标作为当前实际位置更新到PID控制器
        app_pid_update_position(coord.x, coord.y);
    }
    // 绿色激光处理：设置目标位置（PID设定值）
    else if(coord.type == GREEN_LASER_ID)
    {
        // 将绿色激光坐标作为新的跟踪目标
        app_pid_set_target(coord.x, coord.y);
    }
}

/**
 * @brief PID控制任务函数
 * @details 由定时器回调定期执行，实现周期性PID控制
 *          只有在PID运行状态下才执行计算和控制
 * @param timer 定时器对象指针
 * @param userData 用户数据指针（本函数中未使用）
 * @note 执行周期：10ms（由sample_time决定）
 *       任务会在执行完成后重新启动自己，实现周期性调用
 */
void app_pid_task(MultiTimer *timer, void *userData)
{
    // 检查PID控制是否处于运行状态
    if (pid_running)
    {
        // 执行PID控制计算，输出电机控制量
        app_pid_calc();

        // 重新启动定时器，实现周期性调用
        // 周期时间 = sample_time * 1000 (转换为毫秒)
        multiTimerStart(&mt_pid, (uint32_t)(pid_params_x.sample_time * 1000), app_pid_task, NULL);
    }
}

/**
 * @brief 启动PID控制
 * @details 初始化并启动PID控制系统，开始激光跟踪
 *          设置激光坐标回调，重置PID状态，启动控制任务
 * @note 如果PID已经在运行，函数会直接返回
 *       启动后系统将开始接收激光坐标并进行跟踪控制
 */
void app_pid_start(void)
{
    // 如果PID控制已经在运行，直接返回
    if (pid_running)
    {
        return;
    }

#if DEBUG_PID
    my_printf(&PID_REPORT_UART, "启动PID控制\n");
#endif

    // 设置激光坐标回调函数，开始接收摄像头数据
    maixcam_set_callback(pid_laser_coord_callback);

    // 重置PID控制器状态，清除历史积分和微分项
    pid_reset(&pid_x);
    pid_reset(&pid_y);

    // 设置PID运行标志为真
    pid_running = true;

    // 启动PID控制定时器，开始周期性控制任务
    multiTimerStart(&mt_pid, (uint32_t)(pid_params_x.sample_time * 1000), app_pid_task, NULL);

    // 如果启用PID信息上报，启动上报定时器
#if PID_REPORT_ENABLE
    multiTimerStart(&mt_pid_report, PID_REPORT_PERIOD, app_pid_report_task, NULL);
#endif
}

/**
 * @brief ֹͣPID����
 */
void app_pid_stop(void)
{
    // ���PID������ֹͣ��ֱ�ӷ���
    if (!pid_running)
    {
        return;
    }

#if DEBUG_PID
    my_printf(&PID_REPORT_UART, "ֹͣPID����\n");
#endif

    // ֹͣ���
    Motor_Stop();

    // ֹͣPID����ʱ��
    multiTimerStop(&mt_pid);

    // �������PID��Ϣ�ϱ���ֹͣ�ϱ���ʱ��
#if PID_REPORT_ENABLE
    multiTimerStop(&mt_pid_report);
#endif

    // ���PID���б�־
    pid_running = false;

    // �ָ�Ĭ�ϻص�����
    maixcam_set_callback(NULL);
}

/**
 * @brief ����X��PID����
 * @param params �µ�PID�����ṹ��
 */
void app_pid_set_x_params(PidParams_t params)
{
    // ���²����ṹ��
    pid_params_x = params;

    // ����PID����������
    pid_set_params(&pid_x, params.kp, params.ki, params.kd);
    pid_set_limit(&pid_x, params.out_max);
}

/**
 * @brief ����Y��PID����
 * @param params �µ�PID�����ṹ��
 */
void app_pid_set_y_params(PidParams_t params)
{
    // ���²����ṹ��
    pid_params_y = params;

    // ����PID����������
    pid_set_params(&pid_y, params.kp, params.ki, params.kd);
    pid_set_limit(&pid_y, params.out_max);
}

/**
 * @brief ������λ��Ŀ��ֵ
 * @param scale_value �Ŵ���ֵ
 * @return ʵ��ֵ
 */
int app_pid_parse_target(int scale_value)
{
    // ������λ�����ݸ�ʽ��ת��Ϊʵ������ֵ
    return scale_value / PID_REPORT_SCALE;
}

/**
 * @brief PID��Ϣ�ϱ�����
 * ����ָ����ʽ���PID��������Ŀ��ֵ��ʵ��ֵ
 */
void app_pid_report(void)
{
    // ��PIDĿ��ֵ��ʵ��ֵ�Ŵ�ת��Ϊ����
    int scaled_target_x = (int)(target_x * PID_REPORT_SCALE);
    int scaled_current_x = (int)(current_x * PID_REPORT_SCALE);
    int scaled_target_y = (int)(target_y * PID_REPORT_SCALE);
    int scaled_current_y = (int)(current_y * PID_REPORT_SCALE);

    // ����{name}a,b\n��ʽ���PID��Ϣ
    my_printf(&PID_REPORT_UART, "{X}%d,%d\n", scaled_target_x, scaled_current_x);
    my_printf(&PID_REPORT_UART, "{Y}%d,%d\n", scaled_target_y, scaled_current_y);
    my_printf(&PID_REPORT_UART, "{V}%d,%d\n", motor_x, motor_y);

    // ������������Ϣ
#if PID_DEBUG_ENABLE
    my_printf(&PID_REPORT_UART, "{PX}%.2f,%.2f,%.2f\n", pid_x.p_out, pid_x.i_out, pid_x.d_out);
    my_printf(&PID_REPORT_UART, "{PY}%.2f,%.2f,%.2f\n", pid_y.p_out, pid_y.i_out, pid_y.d_out);
#endif
}

/**
 * @brief PID��Ϣ�ϱ�����
 * �ɶ�ʱ���ص���������ִ��PID��Ϣ�ϱ�
 */
void app_pid_report_task(MultiTimer *timer, void *userData)
{
#if PID_REPORT_ENABLE
    // ִ��PID��Ϣ�ϱ�
    app_pid_report();

    // ����������ʱ����ʵ�����ڵ���
    multiTimerStart(&mt_pid_report, PID_REPORT_PERIOD, app_pid_report_task, NULL);
#endif
}

/**
 * @brief ������λ��ָ��
 * @param cmd ��λ��ָ���ַ���
 */
void app_pid_parse_cmd(char *cmd)
{
    int val1, val2, val3;

    // ������λ�����õ�Ŀ��λ��
    if (sscanf(cmd, "T:%d,%d", &val1, &val2) == 2)
    {
        target_x = app_pid_parse_target(val1);
        target_y = app_pid_parse_target(val2);

        // ����PIDĿ��ֵ
        pid_set_target(&pid_x, (float)target_x);
        pid_set_target(&pid_y, (float)target_y);

#if DEBUG_PID
        my_printf(&PID_REPORT_UART, "�յ�Ŀ��λ������: X=%d, Y=%d\n", target_x, target_y);
#endif
    }
    // ����X��PID�������� - PX:kp,ki,kd
    else if (sscanf(cmd, "PX:%d,%d,%d", &val1, &val2, &val3) == 3)
    {
        PidParams_t new_params_x = pid_params_x;
        new_params_x.kp = val1 / 100.0f;
        new_params_x.ki = val2 / 100.0f;
        new_params_x.kd = val3 / 100.0f;
        app_pid_set_x_params(new_params_x);
#if DEBUG_PID
        my_printf(&PID_REPORT_UART, "����X��PID����: kp=%.2f, ki=%.2f, kd=%.2f\n",
                  new_params_x.kp, new_params_x.ki, new_params_x.kd);
#endif
    }
    // ����Y��PID�������� - PY:kp,ki,kd
    else if (sscanf(cmd, "PY:%d,%d,%d", &val1, &val2, &val3) == 3)
    {
        PidParams_t new_params_y = pid_params_y;
        new_params_y.kp = val1 / 100.0f;
        new_params_y.ki = val2 / 100.0f;
        new_params_y.kd = val3 / 100.0f;
        app_pid_set_y_params(new_params_y);
#if DEBUG_PID
        my_printf(&PID_REPORT_UART, "����Y��PID����: kp=%.2f, ki=%.2f, kd=%.2f\n",
                  new_params_y.kp, new_params_y.ki, new_params_y.kd);
#endif
    }
    // ��������/ֹͣ����
    else if (strncmp(cmd, "START", 5) == 0)
    {
        app_pid_start();
    }
    else if (strncmp(cmd, "STOP", 4) == 0)
    {
        app_pid_stop();
    }
    // ֱ������λ������ R: X=110, Y=102
    else if (sscanf(cmd, "R: X=%d, Y=%d", &val1, &val2) == 2)
    {
        target_x = val1;
        target_y = val2;

        // ����PIDĿ��ֵ
        pid_set_target(&pid_x, (float)target_x);
        pid_set_target(&pid_y, (float)target_y);

#if DEBUG_PID
        my_printf(&PID_REPORT_UART, "ֱ������Ŀ��λ��: X=%d, Y=%d\n", target_x, target_y);
        // ��һ�ζ�ȡ������ֵ
        app_pid_init();
        // ����Ĭ��Ŀ��λ��
        app_pid_set_target(target_x, target_y);
#endif
        // ���PIDδ�������Զ�����
        if (!pid_running)
        {
            app_pid_start();
        }
    }
}
